#!/usr/bin/env python3
"""
Simple PyInstaller build script for FusionClient
No protection, no obfuscation - just a clean build
"""

import os
import sys
import subprocess
import shutil

def build_fusion_client():
    """Build FusionClient with PyInstaller"""
    
    print("🚀 Building FusionClient with PyInstaller...")
    
    # Clean previous builds
    if os.path.exists("dist"):
        shutil.rmtree("dist")
        print("✅ Cleaned previous dist folder")
    
    if os.path.exists("build"):
        shutil.rmtree("build")
        print("✅ Cleaned previous build folder")
    
    # PyInstaller command
    cmd = [
        "pyinstaller",
        "--onefile",                    # Single executable
        "--windowed",                   # No console window
        "--name=FusionClient",          # Output name
        "--icon=fusion.ico",            # Icon (if exists)
        "--add-data=sounds;sounds",     # Include sounds folder
        "FusionClient.py"
    ]
    
    # Remove icon parameter if file doesn't exist
    if not os.path.exists("fusion.ico"):
        cmd.remove("--icon=fusion.ico")
        print("⚠️  No fusion.ico found, building without icon")
    
    # Remove sounds parameter if folder doesn't exist
    if not os.path.exists("sounds"):
        cmd.remove("--add-data=sounds;sounds")
        print("⚠️  No sounds folder found, building without sounds")
    
    try:
        # Run PyInstaller
        print(f"🔨 Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        print("✅ Build successful!")
        print(f"📦 Executable created: dist/FusionClient.exe")
        
        # Show file size
        exe_path = "dist/FusionClient.exe"
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📏 File size: {size_mb:.1f} MB")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed!")
        print(f"Error: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False
    
    except FileNotFoundError:
        print("❌ PyInstaller not found!")
        print("Install it with: pip install pyinstaller")
        return False

def install_requirements():
    """Install required packages"""
    requirements = [
        "pyinstaller",
        "customtkinter", 
        "pymem",
        "psutil",
        "keyboard",
        "pillow",
        "requests",
        "pygame"
    ]
    
    print("📦 Installing requirements...")
    for req in requirements:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", req], 
                         check=True, capture_output=True)
            print(f"✅ {req}")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {req}")
            return False
    
    return True

if __name__ == "__main__":
    print("🎯 FusionClient Build Script")
    print("=" * 40)
    
    # Check if we should install requirements
    if "--install" in sys.argv:
        if not install_requirements():
            sys.exit(1)
    
    # Build the client
    if build_fusion_client():
        print("\n🎉 Build completed successfully!")
        print("You can find FusionClient.exe in the dist/ folder")
    else:
        print("\n💥 Build failed!")
        sys.exit(1)
